class TankLevelUpdate {
  final String tankId;
  final double currentLevel;

  TankLevelUpdate({
    required this.tankId,
    required this.currentLevel,
  });

  factory TankLevelUpdate.fromJson(Map<String, dynamic> json) {
    return TankLevelUpdate(
      tankId: json['tank_id'] ?? '',
      currentLevel: (json['current_level'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tank_id': tankId,
      'current_level': currentLevel,
    };
  }

  @override
  String toString() {
    return 'TankLevelUpdate(tankId: $tankId, currentLevel: $currentLevel)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TankLevelUpdate &&
        other.tankId == tankId &&
        other.currentLevel == currentLevel;
  }

  @override
  int get hashCode => tankId.hashCode ^ currentLevel.hashCode;
}
