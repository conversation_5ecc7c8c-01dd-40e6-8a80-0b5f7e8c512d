import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class LocalNotificationService {
  static LocalNotificationService? _instance;
  static FlutterLocalNotificationsPlugin? _flutterLocalNotificationsPlugin;

  // Singleton pattern
  static LocalNotificationService get instance {
    _instance ??= LocalNotificationService._internal();
    return _instance!;
  }

  LocalNotificationService._internal();

  /// Initialize the notification service
  Future<void> initialize() async {
    try {
      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // Combined initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize the plugin
      await _flutterLocalNotificationsPlugin!.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Request permissions for Android 13+
      await _requestPermissions();

      debugPrint('✅ Local notification service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing local notification service: $e');
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    try {
      if (_flutterLocalNotificationsPlugin != null) {
        // Request permissions for Android
        await _flutterLocalNotificationsPlugin!
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();

        // Request permissions for iOS
        await _flutterLocalNotificationsPlugin!
            .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(
              alert: true,
              badge: true,
              sound: true,
            );
      }
    } catch (e) {
      debugPrint('❌ Error requesting notification permissions: $e');
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    debugPrint('🔔 Notification tapped: ${notificationResponse.payload}');
    // Handle notification tap if needed
    // You can navigate to specific screens based on the payload
  }

  /// Show a general notification
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      if (_flutterLocalNotificationsPlugin == null) {
        debugPrint('❌ Notification plugin not initialized');
        return;
      }

      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'general_notifications',
        'General Notifications',
        channelDescription: 'General app notifications',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        icon: '@mipmap/ic_launcher',
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin!.show(
        id,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );

      debugPrint('✅ Notification shown: $title');
    } catch (e) {
      debugPrint('❌ Error showing notification: $e');
    }
  }

  /// Show a tank level update notification
  Future<void> showTankLevelNotification({
    required String tankId,
    required double currentLevel,
  }) async {
    try {
      // Create a meaningful notification message
      final String title = 'Tank Level Update';
      final String body = 'Tank ${tankId.substring(tankId.length - 4)} water level updated to ${currentLevel.toStringAsFixed(1)}L';
      
      // Use tank ID hash as notification ID to avoid duplicates
      final int notificationId = tankId.hashCode.abs();

      await showNotification(
        id: notificationId,
        title: title,
        body: body,
        payload: 'tank_level_update:$tankId:$currentLevel',
      );

      debugPrint('✅ Tank level notification shown for tank: $tankId');
    } catch (e) {
      debugPrint('❌ Error showing tank level notification: $e');
    }
  }

  /// Show a general notification message
  Future<void> showGeneralNotification({
    required String message,
  }) async {
    try {
      final int notificationId = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      await showNotification(
        id: notificationId,
        title: 'MyTank Notification',
        body: message,
        payload: 'general_notification:$notificationId',
      );

      debugPrint('✅ General notification shown: $message');
    } catch (e) {
      debugPrint('❌ Error showing general notification: $e');
    }
  }

  /// Cancel a specific notification
  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin?.cancel(id);
      debugPrint('✅ Notification cancelled: $id');
    } catch (e) {
      debugPrint('❌ Error cancelling notification: $e');
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin?.cancelAll();
      debugPrint('✅ All notifications cancelled');
    } catch (e) {
      debugPrint('❌ Error cancelling all notifications: $e');
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      if (_flutterLocalNotificationsPlugin == null) return false;

      // Check Android permissions
      final androidImplementation = _flutterLocalNotificationsPlugin!
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      
      if (androidImplementation != null) {
        final bool? enabled = await androidImplementation.areNotificationsEnabled();
        return enabled ?? false;
      }

      // For iOS, assume enabled if plugin is initialized
      return true;
    } catch (e) {
      debugPrint('❌ Error checking notification permissions: $e');
      return false;
    }
  }
}
