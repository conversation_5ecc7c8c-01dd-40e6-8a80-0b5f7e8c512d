import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// AuthManager handles all authentication state persistence using SharedPreferences
/// This includes token, user information, and login state management
class AuthManager {
  // Keys for SharedPreferences
  static const String _tokenKey = 'auth_token';
  static const String _userNameKey = 'user_name';
  static const String _userIdKey = 'user_id';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _loginTimestampKey = 'login_timestamp';

  /// Save complete authentication state
  static Future<void> saveAuthState({
    required String token,
    String? userName,
    String? userId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save all authentication data
      await prefs.setString(_tokenKey, token);
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setInt(
        _loginTimestampKey,
        DateTime.now().millisecondsSinceEpoch,
      );

      if (userName != null) {
        await prefs.setString(_userNameKey, userName);
      }

      if (userId != null) {
        await prefs.setString(_userIdKey, userId);
      }

      debugPrint('✅ AuthManager: Authentication state saved successfully');
      debugPrint(
        '🔑 Token saved: ${token.length > 15 ? '${token.substring(0, 15)}...' : token}',
      );
      debugPrint('👤 User name: ${userName ?? 'Not provided'}');
      debugPrint('🆔 User ID: ${userId ?? 'Not provided'}');
    } catch (e) {
      debugPrint('❌ AuthManager: Error saving auth state: $e');
      rethrow;
    }
  }

  /// Get authentication token
  static Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      debugPrint('❌ AuthManager: Error getting token: $e');
      return null;
    }
  }

  /// Get user name
  static Future<String?> getUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userNameKey);
    } catch (e) {
      debugPrint('❌ AuthManager: Error getting user name: $e');
      return null;
    }
  }

  /// Get user ID
  static Future<String?> getUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userIdKey);
    } catch (e) {
      debugPrint('❌ AuthManager: Error getting user ID: $e');
      return null;
    }
  }

  /// Check if user is logged in
  static Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isLoggedInKey) ?? false;
    } catch (e) {
      debugPrint('❌ AuthManager: Error checking login status: $e');
      return false;
    }
  }

  /// Get login timestamp
  static Future<DateTime?> getLoginTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_loginTimestampKey);
      return timestamp != null
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : null;
    } catch (e) {
      debugPrint('❌ AuthManager: Error getting login timestamp: $e');
      return null;
    }
  }

  /// Get complete authentication state
  static Future<AuthState> getAuthState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final token = prefs.getString(_tokenKey);
      final userName = prefs.getString(_userNameKey);
      final userId = prefs.getString(_userIdKey);
      final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      final timestampMs = prefs.getInt(_loginTimestampKey);
      final loginTimestamp =
          timestampMs != null
              ? DateTime.fromMillisecondsSinceEpoch(timestampMs)
              : null;

      return AuthState(
        token: token,
        userName: userName,
        userId: userId,
        isLoggedIn: isLoggedIn,
        loginTimestamp: loginTimestamp,
      );
    } catch (e) {
      debugPrint('❌ AuthManager: Error getting auth state: $e');
      return AuthState.empty();
    }
  }

  /// Update user information (name and/or ID)
  static Future<void> updateUserInfo({String? userName, String? userId}) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (userName != null) {
        await prefs.setString(_userNameKey, userName);
        debugPrint('✅ AuthManager: User name updated: $userName');
      }

      if (userId != null) {
        await prefs.setString(_userIdKey, userId);
        debugPrint('✅ AuthManager: User ID updated: $userId');
      }
    } catch (e) {
      debugPrint('❌ AuthManager: Error updating user info: $e');
      rethrow;
    }
  }

  /// Clear all authentication data (logout)
  static Future<void> clearAuthState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove all authentication-related keys
      await Future.wait([
        prefs.remove(_tokenKey),
        prefs.remove(_userNameKey),
        prefs.remove(_userIdKey),
        prefs.remove(_isLoggedInKey),
        prefs.remove(_loginTimestampKey),
      ]);

      debugPrint('✅ AuthManager: Authentication state cleared successfully');
    } catch (e) {
      debugPrint('❌ AuthManager: Error clearing auth state: $e');
      rethrow;
    }
  }

  /// Check if the stored authentication data is valid (has token and is marked as logged in)
  static Future<bool> hasValidAuthState() async {
    try {
      final authState = await getAuthState();
      return authState.isValid;
    } catch (e) {
      debugPrint('❌ AuthManager: Error validating auth state: $e');
      return false;
    }
  }

  /// Debug method to print all stored authentication data
  static Future<void> debugStoredData() async {
    try {
      debugPrint('🔍 ========== AUTHMANAGER DEBUG ==========');
      final prefs = await SharedPreferences.getInstance();

      debugPrint(
        '🔑 Token: ${prefs.getString(_tokenKey) != null ? 'Present' : 'Missing'}',
      );
      debugPrint('👤 User Name: ${prefs.getString(_userNameKey) ?? 'Not set'}');
      debugPrint('🆔 User ID: ${prefs.getString(_userIdKey) ?? 'Not set'}');
      debugPrint('✅ Is Logged In: ${prefs.getBool(_isLoggedInKey) ?? false}');

      final timestampMs = prefs.getInt(_loginTimestampKey);
      if (timestampMs != null) {
        final loginTime = DateTime.fromMillisecondsSinceEpoch(timestampMs);
        final timeSinceLogin = DateTime.now().difference(loginTime);
        debugPrint('📅 Login Time: $loginTime');
        debugPrint(
          '⏰ Time Since Login: ${timeSinceLogin.inHours}h ${timeSinceLogin.inMinutes % 60}m',
        );
      } else {
        debugPrint('📅 Login Time: Not set');
      }

      final authState = await getAuthState();
      debugPrint('✅ Auth State Valid: ${authState.isValid}');
      debugPrint('🔍 =======================================');
    } catch (e) {
      debugPrint('❌ AuthManager: Error debugging stored data: $e');
    }
  }

  /// Test method to verify SharedPreferences functionality
  static Future<bool> testSharedPreferences() async {
    try {
      debugPrint('🧪 Testing SharedPreferences functionality...');
      final prefs = await SharedPreferences.getInstance();

      // Test write
      const testKey = 'auth_test_key';
      final testValue = 'test_value_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString(testKey, testValue);

      // Test read
      final readValue = prefs.getString(testKey);
      final success = readValue == testValue;

      // Cleanup
      await prefs.remove(testKey);

      debugPrint('✅ SharedPreferences test: ${success ? 'PASSED' : 'FAILED'}');
      return success;
    } catch (e) {
      debugPrint('❌ SharedPreferences test failed: $e');
      return false;
    }
  }
}

/// Data class to hold authentication state
class AuthState {
  final String? token;
  final String? userName;
  final String? userId;
  final bool isLoggedIn;
  final DateTime? loginTimestamp;

  const AuthState({
    this.token,
    this.userName,
    this.userId,
    this.isLoggedIn = false,
    this.loginTimestamp,
  });

  /// Create an empty auth state
  factory AuthState.empty() {
    return const AuthState();
  }

  /// Check if the auth state is valid (has token and is logged in)
  bool get isValid => token != null && token!.isNotEmpty && isLoggedIn;

  /// Check if user information is complete
  bool get hasUserInfo => userName != null && userId != null;

  @override
  String toString() {
    return 'AuthState(token: ${token != null ? '${token!.substring(0, token!.length > 15 ? 15 : token!.length)}...' : 'null'}, '
        'userName: $userName, userId: $userId, isLoggedIn: $isLoggedIn, '
        'loginTimestamp: $loginTimestamp)';
  }
}
