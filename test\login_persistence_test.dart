import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mytank/utilities/auth_manager.dart';
import 'package:mytank/providers/auth_provider.dart';

void main() {
  group('Login Persistence Tests', () {
    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    test('should save and restore authentication state correctly', () async {
      // === STEP 1: Save authentication state ===
      const testToken = 'test_token_12345';
      const testUserName = 'Test User';
      const testUserId = 'user_123';

      await AuthManager.saveAuthState(
        token: testToken,
        userName: testUserName,
        userId: testUserId,
      );

      // === STEP 2: Verify state was saved ===
      final savedState = await AuthManager.getAuthState();
      expect(savedState.isValid, isTrue);
      expect(savedState.token, equals(testToken));
      expect(savedState.userName, equals(testUserName));
      expect(savedState.userId, equals(testUserId));
      expect(savedState.isLoggedIn, isTrue);

      // === STEP 3: Create new AuthProvider and initialize (simulating app restart) ===
      final authProvider = AuthProvider();

      // Before initialization, should not be authenticated
      expect(authProvider.isAuthenticated, isFalse);
      expect(authProvider.accessToken, isNull);
      expect(authProvider.userName, isNull);
      expect(authProvider.userId, isNull);

      // Initialize the provider (this is what happens in splash screen)
      await authProvider.initialize();

      // After initialization, should have loaded the saved data
      // Note: In offline mode, the user should remain authenticated with saved credentials
      // The token validation might fail due to network, but the data should be loaded
      expect(authProvider.accessToken, equals(testToken));
      expect(authProvider.userName, equals(testUserName));
      expect(authProvider.userId, equals(testUserId));

      // The user might not be marked as authenticated if server validation failed,
      // but the data should be preserved for offline use
    });

    test('should handle missing authentication state gracefully', () async {
      // === STEP 1: Ensure no saved state ===
      final authState = await AuthManager.getAuthState();
      expect(authState.isValid, isFalse);

      // === STEP 2: Initialize AuthProvider ===
      final authProvider = AuthProvider();
      await authProvider.initialize();

      // Should not be authenticated
      expect(authProvider.isAuthenticated, isFalse);
      expect(authProvider.accessToken, isNull);
      expect(authProvider.userName, isNull);
      expect(authProvider.userId, isNull);
    });

    test('should handle corrupted authentication state', () async {
      // === STEP 1: Set up corrupted state ===
      SharedPreferences.setMockInitialValues({
        'auth_token': 'valid_token',
        'is_logged_in': false, // Inconsistent state
        // user_name is missing (simulating corrupted state)
      });

      // === STEP 2: Initialize AuthProvider ===
      final authProvider = AuthProvider();
      await authProvider.initialize();

      // Should not be authenticated due to invalid state
      expect(authProvider.isAuthenticated, isFalse);
    });

    test(
      'should preserve login state across multiple initializations',
      () async {
        // === STEP 1: Save authentication state ===
        const testToken = 'persistent_token_67890';
        const testUserName = 'Persistent User';
        const testUserId = 'persistent_user_456';

        await AuthManager.saveAuthState(
          token: testToken,
          userName: testUserName,
          userId: testUserId,
        );

        // === STEP 2: Multiple initialization cycles ===
        for (int i = 0; i < 3; i++) {
          final authProvider = AuthProvider();
          await authProvider.initialize();

          // Should preserve the saved data each time
          expect(authProvider.accessToken, equals(testToken));
          expect(authProvider.userName, equals(testUserName));
          expect(authProvider.userId, equals(testUserId));
        }
      },
    );

    test('should clear authentication state on logout', () async {
      // === STEP 1: Save authentication state ===
      await AuthManager.saveAuthState(
        token: 'token_to_be_cleared',
        userName: 'User To Logout',
        userId: 'logout_user_789',
      );

      // === STEP 2: Initialize and verify data is loaded ===
      final authProvider = AuthProvider();
      await authProvider.initialize();
      expect(authProvider.accessToken, isNotNull);

      // === STEP 3: Logout ===
      await authProvider.logout();

      // === STEP 4: Verify state is cleared ===
      expect(authProvider.isAuthenticated, isFalse);
      expect(authProvider.accessToken, isNull);
      expect(authProvider.userName, isNull);
      expect(authProvider.userId, isNull);

      // === STEP 5: Verify stored state is also cleared ===
      final storedState = await AuthManager.getAuthState();
      expect(storedState.isValid, isFalse);
    });

    test('should handle SharedPreferences errors gracefully', () async {
      // This test would require mocking SharedPreferences to throw errors
      // For now, we'll test that AuthManager methods handle errors

      // Test with empty values (simulating missing data)
      SharedPreferences.setMockInitialValues({
        'user_name': '',
        // auth_token and is_logged_in are missing
      });

      final authState = await AuthManager.getAuthState();
      expect(authState.isValid, isFalse);

      final authProvider = AuthProvider();
      await authProvider.initialize();
      expect(authProvider.isAuthenticated, isFalse);
    });

    test('should test SharedPreferences functionality', () async {
      // Test the AuthManager's SharedPreferences test method
      final testResult = await AuthManager.testSharedPreferences();
      expect(testResult, isTrue);
    });

    test('should validate auth state correctly', () async {
      // Test valid state
      await AuthManager.saveAuthState(
        token: 'valid_token',
        userName: 'Valid User',
        userId: 'valid_user_id',
      );

      final hasValidState = await AuthManager.hasValidAuthState();
      expect(hasValidState, isTrue);

      // Test invalid state after clearing
      await AuthManager.clearAuthState();
      final hasValidStateAfterClear = await AuthManager.hasValidAuthState();
      expect(hasValidStateAfterClear, isFalse);
    });
  });

  group('AuthManager Debug Tests', () {
    test('should provide debug information without errors', () async {
      // Save some test data
      await AuthManager.saveAuthState(
        token: 'debug_token',
        userName: 'Debug User',
        userId: 'debug_user_id',
      );

      // These should not throw errors
      expect(() async => await AuthManager.debugStoredData(), returnsNormally);
    });
  });

  group('AuthProvider Debug Tests', () {
    test('should provide debug information without errors', () async {
      final authProvider = AuthProvider();

      // These should not throw errors
      expect(() => authProvider.debugAuthState(), returnsNormally);
      expect(
        () async => await authProvider.debugStoredAuthState(),
        returnsNormally,
      );
      expect(
        () async => await authProvider.testAuthPersistence(),
        returnsNormally,
      );
    });
  });
}
